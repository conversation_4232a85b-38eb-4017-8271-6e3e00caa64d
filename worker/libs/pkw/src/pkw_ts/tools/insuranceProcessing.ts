import { logging, weightedRandomSelection } from 'shared';
import { protocol } from 'proto/gs_protocol';
import INoticeGameInsurance = protocol.INoticeGameInsurance;

export const LOW_OUTS_COUNT = 3;
export const MEDIUM_OUTS_COUNT = 7;
export const HIGH_OUTS_COUNT = 12;

enum SERVER_COVERAGE_AMOUNT {
    LOW = 1/8,
    MEDIUM_LOW = 1/5,
    MEDIUM_HIGH = 1/3,
    BREAK_EAVEN = 1/2,
    FULL_POT = 1,
}

export const calculateInsuranceAmount = (notifyInsuranceMsg: INoticeGameInsurance): number => {
    // convert amounts from cents to dollars
    const limitAmount = notifyInsuranceMsg.limit_amount / 100;
    const potAmount = notifyInsuranceMsg.pot_amount / 100;
    const chipInsurance = potAmount + limitAmount;

    const outsCount = notifyInsuranceMsg.outs.length;
    const foldCardsCount = notifyInsuranceMsg.foldCards.length;
    const pablicCardsCount = notifyInsuranceMsg.public_cards.length;

    const shouldBuy = shouldBuyInsurance(outsCount);

    if (!shouldBuy) {
        return 0;
    }

    const heroHoleCardsCount = 2;
    const remainingCards = 52 - foldCardsCount - pablicCardsCount - heroHoleCardsCount;

    const currentOdds = calculateCurrentOdds(remainingCards, outsCount);

    if (currentOdds === 0) {
        return 0;
    }

    const coveregeAmountsWaights = getCoverageAmountWeights(outsCount);
    const coverageAmount : SERVER_COVERAGE_AMOUNT = weightedRandomSelection(
        coveregeAmountsWaights,
        (entry) => entry.weight,
    ).covarageAmount;

    let insuranceAmount = 0;

    if (coverageAmount === SERVER_COVERAGE_AMOUNT.BREAK_EAVEN) {
        const totalInvestmentAmount = notifyInsuranceMsg.total_inv_amount / 100;

        insuranceAmount = totalInvestmentAmount / currentOdds;
    } else {
        insuranceAmount = chipInsurance * coverageAmount / currentOdds;
    }

    // round to 2 decimal places
    const roundedInsuranceAmount = Math.round((insuranceAmount + Number.EPSILON) * 100) / 100;

    logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
        shouldBuy,
        currentOdds,
        coverageAmount,
        roundedInsuranceAmount,
    });

    return roundedInsuranceAmount;
};

const calculateCurrentOdds = (remainingCards: number, outsCount: number): number => {
    const odds = (remainingCards / outsCount) * 0.95 - 1;

    //rounded down to 1 decimal place
    let roundedOdds = Math.round((odds + Number.EPSILON) * 10) / 10;

    if (roundedOdds < 0.1) {
        roundedOdds = 0;
    }

    logging.withTag('INSURANCE_DECISION').info('Current odds calculated', {
        remainingCards,
        outsCount,
        odds,
        roundedOdds,
    });

    return roundedOdds;
};

const shouldBuyInsurance = (outsCount: number): boolean => {
    let buyProbability: number;

    if (outsCount <= LOW_OUTS_COUNT) {
        buyProbability = 0.1; // Low probability - opponent has few ways to improve
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        buyProbability = 0.25; // Medium-low probability - moderate opponent threat
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        buyProbability = 0.35; // Medium probability - significant opponent threat
    } else {
        buyProbability = 0.55; // High probability - opponent has many ways to improve
    }

    return buyProbability > Math.random();
};

const getCoverageAmountWeights = (outsCount: number): { covarageAmount: number; weight: number }[] => {
    if (outsCount <= LOW_OUTS_COUNT) {
        // Few opponent outs - lower risk, prefer smaller coverage amounts
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.LOW,
                weight: 50,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 35,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 15,
            },
        ];
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        // Medium opponent outs - moderate risk, balanced coverage
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.LOW,
                weight: 30,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 40,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 10,
            },
        ];
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        // More opponent outs - higher risk, prefer medium to high coverage
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 25,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 35,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 30,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.FULL_POT,
                weight: 10,
            },
        ];
    } else {
        // Many opponent outs - high risk, prefer higher coverage amounts
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 40,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.FULL_POT,
                weight: 40,
            },
        ];
    }
};
